import { UsersService } from './users.service';
import { CreateUserDto } from './dto/createUser.dto';
import { UpdateUserDto } from './dto/updateUser.dto';
import { IUserResponse, IUserQuery } from './interfaces/users.interface';
export declare class UsersController {
    private readonly usersService;
    constructor(usersService: UsersService);
    createUser(createUserDto: CreateUserDto): Promise<{
        success: boolean;
        message: string;
        data: IUserResponse;
    }>;
    getUsers(query: IUserQuery): Promise<{
        success: boolean;
        message: string;
        count: number;
        total: number;
        totalPages: number;
        currentPage: number;
        data: IUserResponse[];
        pagination: {
            page: number;
            limit: number;
            totalPages: number;
        };
    }>;
    getUser(id: string): Promise<{
        success: boolean;
        message: string;
        data: IUserResponse;
    }>;
    updateUser(id: string, updateUserDto: UpdateUserDto): Promise<{
        success: boolean;
        message: string;
        data: IUserResponse;
    }>;
    deleteUser(id: string): Promise<{
        success: boolean;
        message: string;
        data: IUserResponse;
    }>;
}
