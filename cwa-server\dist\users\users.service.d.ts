import { Model } from 'mongoose';
import { IUser, IUserResponse, IUserQuery, IUserPaginationResponse } from './interfaces/users.interface';
import { CreateUserDto } from './dto/createUser.dto';
import { UpdateUserDto } from './dto/updateUser.dto';
export declare class UsersService {
    private userModel;
    constructor(userModel: Model<IUser>);
    createUser(createUserDto: CreateUserDto): Promise<IUserResponse>;
    findByEmail(email: string): Promise<IUserResponse | null>;
    findByEmailWithPassword(email: string): Promise<IUser | null>;
    findById(id: string): Promise<IUserResponse | null>;
    findAll(query: IUserQuery): Promise<IUserPaginationResponse>;
    updateUser(id: string, updateUserDto: UpdateUserDto): Promise<IUserResponse>;
    deleteUser(id: string): Promise<IUserResponse>;
    updatePassword(id: string, newPassword: string): Promise<void>;
    updateResetToken(email: string, token: string, otp: string, expires: Date): Promise<void>;
    clearResetToken(email: string): Promise<void>;
}
