import { AuthService } from "./auth.service";
import { IAuth } from "./interfaces/auth.interfaces";
import { CreateUserDto } from "src/users/dto/createUser.dto";
import { signInDto } from "./dto/signIn.dto";
import { ForgotPasswordDto } from "./dto/forget-password.dto";
import { ResetPasswordDto } from './dto/reset-password.dto';
import { ChangePasswordDto } from "./dto/change-password.dto";
export declare class AuthController {
    private readonly authService;
    constructor(authService: AuthService);
    signUp(signUpDto: CreateUserDto): Promise<IAuth>;
    signIn(signInDto: signInDto): Promise<IAuth>;
    forgotPassword(forgotPasswordDto: ForgotPasswordDto): Promise<{
        message: string;
    }>;
    resetPassword(resetPasswordDto: ResetPasswordDto): Promise<any>;
    changePassword(req: any, changePasswordDto: ChangePasswordDto): Promise<{
        message: string;
    }>;
}
